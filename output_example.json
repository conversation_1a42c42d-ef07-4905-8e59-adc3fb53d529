{"all": {"name": "Sample Test", "duration": 2, "marks": 24, "total_questions": 6, "sections": [{"section_name": "Section 1", "section_questions": 3, "section_marks": 12, "pre": [], "question_list": [{"type": "mcq_single_correct", "question": {"en": "<p>What is 1 + 1?</p>", "hi": "<p>1 + 1 कितना है?</p>"}, "options": {"en": ["<b>1</b>", "2", "3", "4"], "hi": ["1", "2", "3", "4"]}, "answer": 2, "solution": {"en": "Because 1 + 1 = 2", "hi": "क्योंक<PERSON> 1 + 1 = 2"}, "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": {"en": "What is 2 + 2?", "hi": "2 + 2 कितना है?"}, "options": {"en": ["2", "3", "4", "5"], "hi": ["2", "3", "4", "5"]}, "answer": 3, "solution": {"en": "Because 2 + 2 = 4", "hi": "क्यों<PERSON><PERSON> 2 + 2 = 4"}, "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": {"en": "What is 3 + 3?", "hi": "3 + 3 कितना है?"}, "options": {"en": ["5", "6", "7", "8"], "hi": ["5", "6", "7", "8"]}, "answer": 2, "solution": {"en": "Because 3 + 3 = 6", "hi": "क्यो<PERSON><PERSON><PERSON> 3 + 3 = 6"}, "positive_marks": 4, "negative_makrs": 0}]}, {"section_name": "Section 2", "section_questions": 3, "section_marks": 12, "pre": [], "question_list": [{"type": "mcq_single_correct", "question": {"en": "What is 4 + 4?", "hi": "4 + 4 कितना है?"}, "options": {"en": ["6", "7", "8", "9"], "hi": ["6", "7", "8", "9"]}, "answer": 3, "solution": {"en": "Because 4 + 4 = 8", "hi": "क्योंकि 4 + 4 = 8"}, "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": {"en": "What is 5 + 5?", "hi": "5 + 5 कितना है?"}, "options": {"en": ["9", "10", "11", "12"], "hi": ["9", "10", "11", "12"]}, "answer": 2, "solution": {"en": "Because 5 + 5 = 10", "hi": "क्यो<PERSON><PERSON><PERSON> 5 + 5 = 10"}, "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": {"en": "What is 6 + 6?", "hi": "6 + 6 कितना है?"}, "options": {"en": ["10", "11", "12", "13"], "hi": ["10", "11", "12", "13"]}, "answer": 3, "solution": {"en": "Because 6 + 6 = 12", "hi": "क्य<PERSON><PERSON><PERSON><PERSON> 6 + 6 = 12"}, "positive_marks": 4, "negative_makrs": 0}]}], "instructions": {"en": "", "hi": ""}, "languages": {"en": "English", "hi": "Hindi"}, "primary_language": "en"}, "en": {"name": "Sample Test", "duration": 2, "marks": 24, "total_questions": 6, "sections": [{"section_name": "Section 1", "section_questions": 3, "section_marks": 12, "pre": [], "question_list": [{"type": "mcq_single_correct", "question": "What is 1 + 1?", "options": ["1", "2", "3", "4"], "answer": 2, "solution": "Because 1 + 1 = 2", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "What is 2 + 2?", "options": ["2", "3", "4", "5"], "answer": 3, "solution": "Because 2 + 2 = 4", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "What is 3 + 3?", "options": ["5", "6", "7", "8"], "answer": 2, "solution": "Because 3 + 3 = 6", "positive_marks": 4, "negative_makrs": 0}]}, {"section_name": "Section 2", "section_questions": 3, "section_marks": 12, "pre": [], "question_list": [{"type": "mcq_single_correct", "question": "What is 4 + 4?", "options": ["6", "7", "8", "9"], "answer": 3, "solution": "Because 4 + 4 = 8", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "What is 5 + 5?", "options": ["9", "10", "11", "12"], "answer": 2, "solution": "Because 5 + 5 = 10", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "What is 6 + 6?", "options": ["10", "11", "12", "13"], "answer": 3, "solution": "Because 6 + 6 = 12", "positive_marks": 4, "negative_makrs": 0}]}], "instructions": "", "languages": "English", "primary_language": "en"}, "hi": {"name": "Sample Test", "duration": 2, "marks": 24, "total_questions": 6, "sections": [{"section_name": "Section 1", "section_questions": 3, "section_marks": 12, "pre": [], "question_list": [{"type": "mcq_single_correct", "question": "1 + 1 कितना है?", "options": ["1", "2", "3", "4"], "answer": 2, "solution": "क्योंक<PERSON> 1 + 1 = 2", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "2 + 2 कितना है?", "options": ["2", "3", "4", "5"], "answer": 3, "solution": "क्यों<PERSON><PERSON> 2 + 2 = 4", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "3 + 3 कितना है?", "options": ["5", "6", "7", "8"], "answer": 2, "solution": "क्यो<PERSON><PERSON><PERSON> 3 + 3 = 6", "positive_marks": 4, "negative_makrs": 0}]}, {"section_name": "Section 2", "section_questions": 3, "section_marks": 12, "pre": [], "question_list": [{"type": "mcq_single_correct", "question": "4 + 4 कितना है?", "options": ["6", "7", "8", "9"], "answer": 3, "solution": "क्योंकि 4 + 4 = 8", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "5 + 5 कितना है?", "options": ["9", "10", "11", "12"], "answer": 2, "solution": "क्यो<PERSON><PERSON><PERSON> 5 + 5 = 10", "positive_marks": 4, "negative_makrs": 0}, {"type": "mcq_single_correct", "question": "6 + 6 कितना है?", "options": ["10", "11", "12", "13"], "answer": 3, "solution": "क्य<PERSON><PERSON><PERSON><PERSON> 6 + 6 = 12", "positive_marks": 4, "negative_makrs": 0}]}], "instructions": "", "languages": "Hindi", "primary_language": "hi"}}