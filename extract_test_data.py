#!/usr/bin/env python3
"""
Script to extract test data from Wallah HTML file and convert to JSON format.
Uses BeautifulSoup4 for HTML parsing and preserves HTML content in questions/options/solutions.
"""

import json
import re
from bs4 import BeautifulSoup


def extract_test_data(html_file_path):
    """Extract test data from HTML file and return structured JSON."""
    
    with open(html_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # Extract test metadata
    test_name = "General Awareness Test"  # Based on the content
    duration = 8  # 8 minutes from totalTime = 8 * 60
    
    # Find all questions in JavaScript
    script_content = None
    for script in soup.find_all('script'):
        if script.string and 'questions.push(' in script.string:
            script_content = script.string
            break
    
    if not script_content:
        raise ValueError("Could not find questions data in HTML")
    
    # Extract questions using regex
    question_pattern = r'questions\.push\(\{(.*?)\}\);'
    question_matches = re.findall(question_pattern, script_content, re.DOTALL)
    
    questions_data = []
    sections_info = {}
    
    for match in question_matches:
        question_obj = parse_question_object(match)
        questions_data.append(question_obj)
        
        # Track sections
        section_id = question_obj['section']
        if section_id not in sections_info:
            sections_info[section_id] = {
                'questions': [],
                'name': get_section_name(section_id)
            }
        sections_info[section_id]['questions'].append(question_obj)
    
    # Calculate totals
    total_questions = len(questions_data)
    total_marks = sum(q['pos_marks'] for q in questions_data)
    
    # Build sections
    sections = []
    for section_id, section_data in sections_info.items():
        section_questions = section_data['questions']
        section = {
            "section_name": section_data['name'],
            "section_questions": len(section_questions),
            "section_marks": sum(q['pos_marks'] for q in section_questions),
            "pre": [],
            "question_list": []
        }
        
        for q in section_questions:
            question_item = {
                "type": "mcq_single_correct",
                "question": {
                    "en": q['question_en'],
                    "hi": q['question_hi']
                },
                "options": {
                    "en": q['options_en'],
                    "hi": q['options_hi']
                },
                "answer": convert_answer_to_index(q['correct']),
                "solution": {
                    "en": q['solution_en'],
                    "hi": q['solution_hi']
                },
                "positive_marks": q['pos_marks'],
                "negative_makrs": q['neg_marks']  # Note: keeping the typo from example
            }
            section['question_list'].append(question_item)
        
        sections.append(section)
    
    # Build the complete structure
    result = {
        "all": {
            "name": test_name,
            "duration": duration,
            "marks": int(total_marks),
            "total_questions": total_questions,
            "sections": sections,
            "instructions": {
                "en": "",
                "hi": ""
            },
            "languages": {
                "en": "English",
                "hi": "Hindi"
            },
            "primary_language": "en"
        }
    }
    
    # Add language-specific versions
    result["en"] = create_language_version(result["all"], "en")
    result["hi"] = create_language_version(result["all"], "hi")
    
    return result


def parse_question_object(js_object_str):
    """Parse JavaScript object string to extract question data."""
    
    # Extract individual fields using regex
    def extract_field(field_name, text):
        pattern = rf'{field_name}:\s*"(.*?)"(?=,\s*\w+:|,\s*\}})'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).replace('\\"', '"').replace('\\n', '')
        return ""
    
    def extract_array_field(field_name, text):
        pattern = rf'{field_name}:\s*\[(.*?)\]'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            array_content = match.group(1)
            # Find all quoted strings, handling escaped quotes
            items = []
            current_item = ""
            in_quotes = False
            escaped = False

            for char in array_content:
                if escaped:
                    current_item += char
                    escaped = False
                elif char == '\\':
                    escaped = True
                    current_item += char
                elif char == '"' and not escaped:
                    if in_quotes:
                        # End of quoted string
                        items.append(current_item.replace('\\"', '"').replace('\\n', ''))
                        current_item = ""
                        in_quotes = False
                    else:
                        # Start of quoted string
                        in_quotes = True
                elif in_quotes:
                    current_item += char

            return items
        return []
    
    def extract_simple_field(field_name, text):
        pattern = rf'{field_name}:\s*"?([^",\}}]+)"?'
        match = re.search(pattern, text)
        if match:
            return match.group(1).strip('"')
        return ""
    
    question_data = {
        'id': extract_simple_field('id', js_object_str),
        'section': extract_simple_field('section', js_object_str),
        'question_en': extract_field('question_en', js_object_str),
        'question_hi': extract_field('question_hi', js_object_str),
        'options_en': extract_array_field('options_en', js_object_str),
        'options_hi': extract_array_field('options_hi', js_object_str),
        'solution_en': extract_field('solution_en', js_object_str),
        'solution_hi': extract_field('solution_hi', js_object_str),
        'correct': extract_simple_field('correct', js_object_str),
        'pos_marks': float(extract_simple_field('pos_marks', js_object_str) or 2.0),
        'neg_marks': float(extract_simple_field('neg_marks', js_object_str) or 0.5)
    }
    
    return question_data


def get_section_name(section_id):
    """Get section name based on section ID."""
    section_names = {
        "18": "General Awareness",
        "misc": "Miscellaneous"
    }
    return section_names.get(section_id, f"Section {section_id}")


def convert_answer_to_index(correct_letter):
    """Convert answer letter (a, b, c, d) to index (1, 2, 3, 4)."""
    mapping = {'a': 1, 'b': 2, 'c': 3, 'd': 4}
    return mapping.get(correct_letter.lower(), 1)


def create_language_version(all_data, lang):
    """Create language-specific version of the test data."""
    lang_data = {
        "name": all_data["name"],
        "duration": all_data["duration"],
        "marks": all_data["marks"],
        "total_questions": all_data["total_questions"],
        "sections": [],
        "instructions": all_data["languages"][lang] if lang in all_data["languages"] else "",
        "languages": all_data["languages"][lang] if lang in all_data["languages"] else lang.title(),
        "primary_language": lang
    }
    
    for section in all_data["sections"]:
        lang_section = {
            "section_name": section["section_name"],
            "section_questions": section["section_questions"],
            "section_marks": section["section_marks"],
            "pre": section["pre"],
            "question_list": []
        }
        
        for question in section["question_list"]:
            lang_question = {
                "type": question["type"],
                "question": question["question"][lang] if lang in question["question"] else question["question"]["en"],
                "options": question["options"][lang] if lang in question["options"] else question["options"]["en"],
                "answer": question["answer"],
                "solution": question["solution"][lang] if lang in question["solution"] else question["solution"]["en"],
                "positive_marks": question["positive_marks"],
                "negative_makrs": question["negative_makrs"]
            }
            lang_section["question_list"].append(lang_question)
        
        lang_data["sections"].append(lang_section)
    
    return lang_data


def main():
    """Main function to extract and save test data."""
    input_file = 'mocks_wallah_0a12fb30.html'
    output_file = 'extracted_test_data.json'
    
    try:
        print(f"Extracting test data from {input_file}...")
        test_data = extract_test_data(input_file)
        
        print(f"Saving extracted data to {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print("Extraction completed successfully!")
        print(f"Total questions: {test_data['all']['total_questions']}")
        print(f"Total marks: {test_data['all']['marks']}")
        print(f"Duration: {test_data['all']['duration']} minutes")
        print(f"Sections: {len(test_data['all']['sections'])}")
        
        for i, section in enumerate(test_data['all']['sections']):
            print(f"  Section {i+1}: {section['section_name']} ({section['section_questions']} questions)")
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
