#!/usr/bin/env python3
"""
Script to extract test data from Wallah HTML file and convert to JSON format.
Uses BeautifulSoup4 for HTML parsing and json.loads() for options parsing.
Preserves HTML content in questions/options/solutions.
"""

import json
import re
from bs4 import BeautifulSoup


def extract_test_data(html_file_path):
    """Extract test data from HTML file and return structured JSON."""

    with open(html_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    soup = BeautifulSoup(content, 'html.parser')

    # Extract test metadata from HTML
    title_tag = soup.find('title')
    test_name = title_tag.text if title_tag else "Test"

    # Find the script containing questions data
    script_content = None
    for script in soup.find_all('script'):
        if script.string and 'questions.push(' in script.string:
            script_content = script.string
            break

    if not script_content:
        raise ValueError("Could not find questions data in HTML")

    # Extract duration from totalTime variable
    duration_match = re.search(r'totalTime\s*=\s*(\d+)\s*\*\s*(\d+)', script_content)
    duration = int(duration_match.group(1)) if duration_match else 8

    # Extract sections info
    sections_info = extract_sections_from_script(script_content)

    # Extract questions data using JavaScript parsing approach
    questions_data = extract_questions_from_script(script_content)

    # Organize questions by sections
    organized_sections = organize_questions_by_sections(questions_data, sections_info)
    
    # Calculate totals
    total_questions = len(questions_data)
    total_marks = sum(q['pos_marks'] for q in questions_data)

    # Build sections in the required format
    sections = build_sections_structure(organized_sections)
    
    # Build the complete structure
    result = {
        "all": {
            "name": test_name,
            "duration": duration,
            "marks": int(total_marks),
            "total_questions": total_questions,
            "sections": sections,
            "instructions": {
                "en": "",
                "hi": ""
            },
            "languages": {
                "en": "English",
                "hi": "Hindi"
            },
            "primary_language": "en"
        }
    }
    
    # Add language-specific versions
    result["en"] = create_language_version(result["all"], "en")
    result["hi"] = create_language_version(result["all"], "hi")
    
    return result


def extract_sections_from_script(script_content):
    """Extract section information from JavaScript."""
    sections_info = {}

    # Extract section definitions
    section_pattern = r'sections\["([^"]+)"\]\s*=\s*\{[^}]*name:\s*"([^"]+)"[^}]*\}'
    section_matches = re.findall(section_pattern, script_content)

    for section_id, section_name in section_matches:
        sections_info[section_id] = {
            'name': section_name,
            'questions': []
        }

    return sections_info


def extract_questions_from_script(script_content):
    """Extract questions data from JavaScript using proper parsing."""
    questions_data = []

    # Find all questions.push() calls
    push_pattern = r'questions\.push\(\{([^}]+(?:\{[^}]*\}[^}]*)*)\}\);'
    push_matches = re.findall(push_pattern, script_content, re.DOTALL)

    for match in push_matches:
        question_obj = parse_js_object_properly(match)
        if question_obj:
            questions_data.append(question_obj)

    return questions_data


def parse_js_object_properly(js_object_str):
    """Parse JavaScript object string properly using field extraction."""

    def extract_string_field(field_name, text):
        """Extract string field value."""
        pattern = rf'{field_name}:\s*"((?:[^"\\]|\\.)*)"'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).replace('\\"', '"').replace('\\n', '\n')
        return ""

    def extract_array_field(field_name, text):
        """Extract array field using json.loads() as instructed."""
        # Find the array pattern more carefully
        pattern = rf'{field_name}:\s*(\[(?:[^\[\]]|"[^"]*")*\])'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            try:
                array_str = match.group(1)
                # Prepare string for json.loads() by fixing JavaScript format
                # Replace single quotes with double quotes if any
                array_str = re.sub(r"'([^']*)'", r'"\1"', array_str)
                # Clean up escaped characters for JSON
                array_str = array_str.replace('\\"', '"').replace('\\n', '').replace('\\r', '')
                # Use json.loads() as requested
                return json.loads(array_str)
            except json.JSONDecodeError as e:
                print(f"JSON decode error for {field_name}: {e}")
                # Fallback: manual parsing if json.loads fails
                return parse_array_manually(match.group(1))
        return []

    def extract_simple_field(field_name, text):
        """Extract simple field (non-string)."""
        pattern = rf'{field_name}:\s*([^,\}}]+)'
        match = re.search(pattern, text)
        if match:
            value = match.group(1).strip().strip('"')
            return value
        return ""

    try:
        question_data = {
            'id': extract_simple_field('id', js_object_str),
            'section': extract_simple_field('section', js_object_str),
            'question_en': extract_string_field('question_en', js_object_str),
            'question_hi': extract_string_field('question_hi', js_object_str),
            'options_en': extract_array_field('options_en', js_object_str),
            'options_hi': extract_array_field('options_hi', js_object_str),
            'solution_en': extract_string_field('solution_en', js_object_str),
            'solution_hi': extract_string_field('solution_hi', js_object_str),
            'correct': extract_simple_field('correct', js_object_str),
            'pos_marks': float(extract_simple_field('pos_marks', js_object_str) or 2.0),
            'neg_marks': float(extract_simple_field('neg_marks', js_object_str) or 0.5)
        }
        return question_data
    except Exception as e:
        print(f"Error parsing question object: {e}")
        return None


def parse_array_manually(array_str):
    """Manual array parsing fallback for complex JavaScript arrays."""
    items = []
    array_str = array_str.strip('[]').strip()

    if not array_str:
        return items

    # Use a more sophisticated approach to split array elements
    current_item = ""
    quote_count = 0
    bracket_depth = 0
    in_quotes = False
    escape_next = False

    i = 0
    while i < len(array_str):
        char = array_str[i]

        if escape_next:
            current_item += char
            escape_next = False
        elif char == '\\':
            current_item += char
            escape_next = True
        elif char == '"' and not escape_next:
            current_item += char
            in_quotes = not in_quotes
        elif char == '[' and not in_quotes:
            current_item += char
            bracket_depth += 1
        elif char == ']' and not in_quotes:
            current_item += char
            bracket_depth -= 1
        elif char == ',' and not in_quotes and bracket_depth == 0:
            # End of current item
            item = current_item.strip().strip('"')
            if item:
                items.append(item)
            current_item = ""
        else:
            current_item += char

        i += 1

    # Add the last item
    if current_item.strip():
        item = current_item.strip().strip('"')
        if item:
            items.append(item)

    return items


def organize_questions_by_sections(questions_data, sections_info):
    """Organize questions by their sections."""
    for question in questions_data:
        section_id = question['section']
        if section_id in sections_info:
            sections_info[section_id]['questions'].append(question)

    return sections_info


def build_sections_structure(organized_sections):
    """Build the sections structure in the required format."""
    sections = []

    for section_id, section_data in organized_sections.items():
        section_questions = section_data['questions']
        section = {
            "section_name": section_data['name'],
            "section_questions": len(section_questions),
            "section_marks": sum(q['pos_marks'] for q in section_questions),
            "pre": [],
            "question_list": []
        }

        for q in section_questions:
            question_item = {
                "type": "mcq_single_correct",
                "question": {
                    "en": q['question_en'],
                    "hi": q['question_hi']
                },
                "options": {
                    "en": q['options_en'],
                    "hi": q['options_hi']
                },
                "answer": convert_answer_to_index(q['correct']),
                "solution": {
                    "en": q['solution_en'],
                    "hi": q['solution_hi']
                },
                "positive_marks": q['pos_marks'],
                "negative_makrs": q['neg_marks']  # Note: keeping the typo from example
            }
            section['question_list'].append(question_item)

        sections.append(section)

    return sections


def convert_answer_to_index(correct_letter):
    """Convert answer letter (a, b, c, d) to index (1, 2, 3, 4)."""
    mapping = {'a': 1, 'b': 2, 'c': 3, 'd': 4}
    return mapping.get(correct_letter.lower(), 1)


def create_language_version(all_data, lang):
    """Create language-specific version of the test data."""
    lang_data = {
        "name": all_data["name"],
        "duration": all_data["duration"],
        "marks": all_data["marks"],
        "total_questions": all_data["total_questions"],
        "sections": [],
        "instructions": all_data["languages"][lang] if lang in all_data["languages"] else "",
        "languages": all_data["languages"][lang] if lang in all_data["languages"] else lang.title(),
        "primary_language": lang
    }
    
    for section in all_data["sections"]:
        lang_section = {
            "section_name": section["section_name"],
            "section_questions": section["section_questions"],
            "section_marks": section["section_marks"],
            "pre": section["pre"],
            "question_list": []
        }
        
        for question in section["question_list"]:
            lang_question = {
                "type": question["type"],
                "question": question["question"][lang] if lang in question["question"] else question["question"]["en"],
                "options": question["options"][lang] if lang in question["options"] else question["options"]["en"],
                "answer": question["answer"],
                "solution": question["solution"][lang] if lang in question["solution"] else question["solution"]["en"],
                "positive_marks": question["positive_marks"],
                "negative_makrs": question["negative_makrs"]
            }
            lang_section["question_list"].append(lang_question)
        
        lang_data["sections"].append(lang_section)
    
    return lang_data


def main():
    """Main function to extract and save test data."""
    input_file = 'mocks_wallah_0a12fb30.html'
    output_file = 'extracted_test_data.json'
    
    try:
        print(f"Extracting test data from {input_file}...")
        test_data = extract_test_data(input_file)
        
        print(f"Saving extracted data to {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print("Extraction completed successfully!")
        print(f"Total questions: {test_data['all']['total_questions']}")
        print(f"Total marks: {test_data['all']['marks']}")
        print(f"Duration: {test_data['all']['duration']} minutes")
        print(f"Sections: {len(test_data['all']['sections'])}")
        
        for i, section in enumerate(test_data['all']['sections']):
            print(f"  Section {i+1}: {section['section_name']} ({section['section_questions']} questions)")
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
