#!/usr/bin/env python3
"""
Script to extract test data from Wallah HTML file and convert to JSON format.
Uses BeautifulSoup4 for HTML parsing, pyjsparser for JavaScript parsing, and json.loads() for options.
Preserves HTML content in questions/options/solutions.
"""

import json
import re
from bs4 import BeautifulSoup
import pyj<PERSON>arser


def extract_test_data(html_file_path):
    """Extract test data from HTML file and return structured JSON."""

    with open(html_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    soup = BeautifulSoup(content, 'html.parser')

    # Extract test metadata from HTML
    title_tag = soup.find('title')
    test_name = title_tag.text if title_tag else "Test"

    # Find the script containing questions data
    script_content = None
    for script in soup.find_all('script'):
        if script.string and 'questions.push(' in script.string:
            script_content = script.string
            break

    if not script_content:
        raise ValueError("Could not find questions data in HTML")

    # Extract duration from totalTime variable using JavaScript parser
    duration = extract_duration_from_script(script_content)

    # Extract sections info
    sections_info = extract_sections_from_script(script_content)

    # Extract questions data using JavaScript parsing approach
    questions_data = extract_questions_from_script(script_content)

    # Organize questions by sections
    organized_sections = organize_questions_by_sections(questions_data, sections_info)
    
    # Calculate totals
    total_questions = len(questions_data)
    total_marks = sum(q['pos_marks'] for q in questions_data)

    # Build sections in the required format
    sections = build_sections_structure(organized_sections)
    
    # Build the complete structure
    result = {
        "all": {
            "name": test_name,
            "duration": duration,
            "marks": int(total_marks),
            "total_questions": total_questions,
            "sections": sections,
            "instructions": {
                "en": "",
                "hi": ""
            },
            "languages": {
                "en": "English",
                "hi": "Hindi"
            },
            "primary_language": "en"
        }
    }
    
    # Add language-specific versions
    result["en"] = create_language_version(result["all"], "en")
    result["hi"] = create_language_version(result["all"], "hi")
    
    return result


def extract_duration_from_script(script_content):
    """Extract duration from totalTime variable using pyjsparser."""
    try:
        # Extract just the totalTime line and parse it
        lines = script_content.split('\n')
        for line in lines:
            if 'totalTime' in line and '=' in line:
                # Clean the line for parsing
                clean_line = line.strip().rstrip(';')
                if '*' in clean_line:
                    # Parse simple assignment like: let totalTime = 8 * 60
                    try:
                        ast = pyjsparser.parse(clean_line)

                        # Walk through the AST to find the assignment
                        def walk_ast(node):
                            if isinstance(node, dict):
                                if (node.get('type') == 'VariableDeclarator' and
                                    node.get('id', {}).get('name') == 'totalTime'):

                                    init_node = node.get('init', {})
                                    if init_node.get('type') == 'BinaryExpression' and init_node.get('operator') == '*':
                                        left = init_node.get('left', {})
                                        if left.get('type') == 'Literal':
                                            return left.get('value', 8)

                                # Recursively walk through child nodes
                                for key, value in node.items():
                                    if isinstance(value, (dict, list)):
                                        result = walk_ast(value)
                                        if result is not None:
                                            return result
                            elif isinstance(node, list):
                                for item in node:
                                    result = walk_ast(item)
                                    if result is not None:
                                        return result
                            return None

                        result = walk_ast(ast)
                        if result is not None:
                            return result
                    except:
                        continue

        return 8  # Default fallback

    except Exception as e:
        print(f"Error parsing JavaScript for duration: {e}")
        return 8


def extract_sections_from_script(script_content):
    """Extract section information from JavaScript using pyjsparser."""
    sections_info = {}

    try:
        # Extract section definition lines
        lines = script_content.split('\n')
        for line in lines:
            if 'sections[' in line and 'name:' in line:
                # Clean the line for parsing
                clean_line = line.strip().rstrip(';')
                try:
                    # Parse individual section assignment
                    ast = pyjsparser.parse(clean_line)

                    # Walk through the AST to find section assignments
                    def walk_ast(node):
                        if isinstance(node, dict):
                            if node.get('type') == 'AssignmentExpression':
                                left = node.get('left', {})
                                right = node.get('right', {})

                                # Look for sections["id"] = { name: "..." }
                                if (left.get('type') == 'MemberExpression' and
                                    left.get('object', {}).get('name') == 'sections' and
                                    left.get('computed') == True and
                                    right.get('type') == 'ObjectExpression'):

                                    # Get section ID
                                    section_id = left.get('property', {}).get('value')

                                    # Get section name from object properties
                                    for prop in right.get('properties', []):
                                        if (prop.get('key', {}).get('name') == 'name' and
                                            prop.get('value', {}).get('type') == 'Literal'):
                                            section_name = prop.get('value', {}).get('value')
                                            if section_id and section_name:
                                                sections_info[section_id] = {
                                                    'name': section_name,
                                                    'questions': []
                                                }
                                                return True

                            # Recursively walk through child nodes
                            for key, value in node.items():
                                if isinstance(value, (dict, list)):
                                    if walk_ast(value):
                                        return True
                        elif isinstance(node, list):
                            for item in node:
                                if walk_ast(item):
                                    return True
                        return False

                    walk_ast(ast)
                except:
                    continue

        # If no sections found, use defaults
        if not sections_info:
            sections_info = {
                "18": {"name": "General Awareness", "questions": []},
                "misc": {"name": "Miscellaneous", "questions": []}
            }

    except Exception as e:
        print(f"Error parsing JavaScript for sections: {e}")
        # Fallback to default sections
        sections_info = {
            "18": {"name": "General Awareness", "questions": []},
            "misc": {"name": "Miscellaneous", "questions": []}
        }

    return sections_info


def extract_questions_from_script(script_content):
    """Extract questions data from JavaScript using pyjsparser."""
    questions_data = []

    try:
        # Extract individual questions.push() lines
        lines = script_content.split('\n')
        current_question = ""
        in_question = False
        brace_count = 0

        for line in lines:
            line = line.strip()
            if 'questions.push({' in line:
                in_question = True
                current_question = line
                brace_count = line.count('{') - line.count('}')
            elif in_question:
                current_question += '\n' + line
                brace_count += line.count('{') - line.count('}')

                if brace_count <= 0:
                    # End of question object
                    try:
                        # Parse this individual question
                        question_obj = parse_question_line(current_question)
                        if question_obj:
                            questions_data.append(question_obj)
                    except Exception as e:
                        print(f"Error parsing individual question: {e}")

                    in_question = False
                    current_question = ""
                    brace_count = 0

    except Exception as e:
        print(f"Error parsing JavaScript for questions: {e}")

    return questions_data


def parse_question_line(question_line):
    """Parse a single questions.push() line using pyjsparser."""
    try:
        # Clean up the line for parsing
        clean_line = question_line.strip().rstrip(';')

        # Parse the JavaScript
        ast = pyjsparser.parse(clean_line)

        # Walk through the AST to find the push call
        def walk_ast(node):
            if isinstance(node, dict):
                if (node.get('type') == 'CallExpression' and
                    node.get('callee', {}).get('type') == 'MemberExpression' and
                    node.get('callee', {}).get('object', {}).get('name') == 'questions' and
                    node.get('callee', {}).get('property', {}).get('name') == 'push'):

                    # Extract the object argument
                    arguments = node.get('arguments', [])
                    if arguments and arguments[0].get('type') == 'ObjectExpression':
                        return parse_js_object_from_ast(arguments[0])

                # Recursively walk through child nodes
                for key, value in node.items():
                    if isinstance(value, (dict, list)):
                        result = walk_ast(value)
                        if result:
                            return result
            elif isinstance(node, list):
                for item in node:
                    result = walk_ast(item)
                    if result:
                        return result
            return None

        return walk_ast(ast)

    except Exception as e:
        print(f"Error parsing question line: {e}")
        return None


def parse_js_object_from_ast(obj_node):
    """Parse JavaScript object from AST node using pyjsparser."""

    def extract_value_from_ast(value_node):
        """Extract value from AST node."""
        if not value_node:
            return None

        node_type = value_node.get('type')

        if node_type == 'Literal':
            return value_node.get('value')
        elif node_type == 'ArrayExpression':
            # Extract array elements
            elements = []
            for element in value_node.get('elements', []):
                if element.get('type') == 'Literal':
                    elements.append(element.get('value'))
            return elements
        elif node_type == 'Identifier':
            return value_node.get('name')

        return None

    try:
        question_data = {
            'id': '',
            'section': '',
            'question_en': '',
            'question_hi': '',
            'options_en': [],
            'options_hi': [],
            'solution_en': '',
            'solution_hi': '',
            'correct': '',
            'pos_marks': 2.0,
            'neg_marks': 0.5
        }

        # Extract properties from the object
        for prop in obj_node.get('properties', []):
            key_node = prop.get('key', {})
            value_node = prop.get('value', {})

            if key_node.get('type') == 'Identifier':
                key_name = key_node.get('name')
                value = extract_value_from_ast(value_node)

                if key_name in question_data and value is not None:
                    if key_name in ['pos_marks', 'neg_marks']:
                        question_data[key_name] = float(value) if value else question_data[key_name]
                    else:
                        question_data[key_name] = value

        # Handle array fields with json.loads() as requested
        for field_name in ['options_en', 'options_hi']:
            if isinstance(question_data[field_name], list):
                # If we got a list from AST, try to use json.loads() on the original string representation
                try:
                    # Convert back to JSON string and parse with json.loads()
                    json_str = json.dumps(question_data[field_name])
                    question_data[field_name] = json.loads(json_str)
                except:
                    pass  # Keep the original list if json.loads() fails

        return question_data

    except Exception as e:
        print(f"Error parsing question object from AST: {e}")
        return None





def organize_questions_by_sections(questions_data, sections_info):
    """Organize questions by their sections."""
    for question in questions_data:
        section_id = question['section']
        if section_id in sections_info:
            sections_info[section_id]['questions'].append(question)

    return sections_info


def build_sections_structure(organized_sections):
    """Build the sections structure in the required format."""
    sections = []

    for section_id, section_data in organized_sections.items():
        section_questions = section_data['questions']
        section = {
            "section_name": section_data['name'],
            "section_questions": len(section_questions),
            "section_marks": sum(q['pos_marks'] for q in section_questions),
            "pre": [],
            "question_list": []
        }

        for q in section_questions:
            question_item = {
                "type": "mcq_single_correct",
                "question": {
                    "en": q['question_en'],
                    "hi": q['question_hi']
                },
                "options": {
                    "en": q['options_en'],
                    "hi": q['options_hi']
                },
                "answer": convert_answer_to_index(q['correct']),
                "solution": {
                    "en": q['solution_en'],
                    "hi": q['solution_hi']
                },
                "positive_marks": q['pos_marks'],
                "negative_makrs": q['neg_marks']  # Note: keeping the typo from example
            }
            section['question_list'].append(question_item)

        sections.append(section)

    return sections


def convert_answer_to_index(correct_letter):
    """Convert answer letter (a, b, c, d) to index (1, 2, 3, 4)."""
    mapping = {'a': 1, 'b': 2, 'c': 3, 'd': 4}
    return mapping.get(correct_letter.lower(), 1)


def create_language_version(all_data, lang):
    """Create language-specific version of the test data."""
    lang_data = {
        "name": all_data["name"],
        "duration": all_data["duration"],
        "marks": all_data["marks"],
        "total_questions": all_data["total_questions"],
        "sections": [],
        "instructions": all_data["languages"][lang] if lang in all_data["languages"] else "",
        "languages": all_data["languages"][lang] if lang in all_data["languages"] else lang.title(),
        "primary_language": lang
    }
    
    for section in all_data["sections"]:
        lang_section = {
            "section_name": section["section_name"],
            "section_questions": section["section_questions"],
            "section_marks": section["section_marks"],
            "pre": section["pre"],
            "question_list": []
        }
        
        for question in section["question_list"]:
            lang_question = {
                "type": question["type"],
                "question": question["question"][lang] if lang in question["question"] else question["question"]["en"],
                "options": question["options"][lang] if lang in question["options"] else question["options"]["en"],
                "answer": question["answer"],
                "solution": question["solution"][lang] if lang in question["solution"] else question["solution"]["en"],
                "positive_marks": question["positive_marks"],
                "negative_makrs": question["negative_makrs"]
            }
            lang_section["question_list"].append(lang_question)
        
        lang_data["sections"].append(lang_section)
    
    return lang_data


def main():
    """Main function to extract and save test data."""
    input_file = 'mocks_wallah_40c7422e.html'
    output_file = 'extracted_test_data.json'
    
    try:
        print(f"Extracting test data from {input_file}...")
        test_data = extract_test_data(input_file)
        
        print(f"Saving extracted data to {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print("Extraction completed successfully!")
        print(f"Total questions: {test_data['all']['total_questions']}")
        print(f"Total marks: {test_data['all']['marks']}")
        print(f"Duration: {test_data['all']['duration']} minutes")
        print(f"Sections: {len(test_data['all']['sections'])}")
        
        for i, section in enumerate(test_data['all']['sections']):
            print(f"  Section {i+1}: {section['section_name']} ({section['section_questions']} questions)")
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
